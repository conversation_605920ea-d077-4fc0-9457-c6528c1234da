import { ChatScreen } from '@/features/chats/screens/ChatScreen/ChatScreen'
import { withAuthentication } from '@/hoc/withAuthentication'
import React from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'
function ChatAppScreenWapper() {
  return (
    <SafeAreaView className="bg-white" style={{ flex: 1 }} edges={['left', 'top', 'right']}>
      <ChatScreen />
    </SafeAreaView>
  )
}
export default withAuthentication(ChatAppScreenWapper)
