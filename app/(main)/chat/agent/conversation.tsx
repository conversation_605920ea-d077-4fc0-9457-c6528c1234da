import { Text } from '@/components/ui/Text/Text'
import {
  ConversationStoreContext,
  ConversationStoreProvider,
} from '@/features/chats/conversations/stores/context'
import { View } from 'react-native'

const ConversationAppScreen = () => {
  return (
    <ConversationStoreProvider>
      <View className=" absolute top-4 flex-1  bg-red-500">
        <Text>Conversation</Text>
      </View>
    </ConversationStoreProvider>
  )
}

export default ConversationAppScreen
