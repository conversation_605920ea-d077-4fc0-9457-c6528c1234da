import { ChatAgentScreenComponent } from '@/features/chats/screens/ChatAgentScreen/ChatAgentScreen'
import { withAuthentication } from '@/hoc/withAuthentication'
import { Link } from 'expo-router'
import React from 'react'
import { SafeAreaView } from 'react-native-safe-area-context'
function ChatAgentAppScreen() {
  return (
    <SafeAreaView className="flex-1 bg-white" edges={['left', 'right', 'top']}>
      <Link href={'/chat/agent/inbox'}>click</Link>

      <ChatAgentScreenComponent />
    </SafeAreaView>
  )
}
export default withAuthentication(ChatAgentAppScreen)
