import { Tabs } from 'expo-router'

import ArchiveActiveIcon from '@/assets/icons/archive-active.svg'
import ConversationIcon from '@/assets/icons/chat-2.svg'
import ConversationActiveIcon from '@/assets/icons/chat-active.svg'
import ArchiveIcon from '@/assets/icons/direct-1.svg'
import SettingIcon from '@/assets/icons/setting-2.svg'
import SettingActiveIcon from '@/assets/icons/setting-active.svg'

const ChatAgentScreen = () => {
  return (
    <Tabs screenOptions={{ headerShown: false }}>
      <Tabs.Screen
        name="index"
        options={{
          title: '',
          tabBarIcon: ({ color, focused }) =>
            focused ? <ArchiveActiveIcon color={color} /> : <ArchiveIcon color={color} />,
        }}
      />
      <Tabs.Screen
        name="conversation"
        options={{
          title: '',
          tabBarIcon: ({ color, focused }) =>
            focused ? <ConversationActiveIcon color={color} /> : <ConversationIcon color={color} />,
        }}
      />

      <Tabs.Screen
        name="setting"
        options={{
          title: '',
          tabBarIcon: ({ color, focused }) =>
            focused ? <SettingActiveIcon color={color} /> : <SettingIcon color={color} />,
        }}
      />
    </Tabs>
  )
}

export default ChatAgentScreen
