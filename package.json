{"name": "wap-native", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint", "format": "prettier --write .", "prepare": "husky", "postinstall": "patch-package"}, "dependencies": {"@chatwoot/react-native-widget": "^0.0.21", "@expo/config-plugins": "~54.0.1", "@expo/html-elements": "^0.12.5", "@expo/vector-icons": "^15.0.3", "@gluestack-ui/accordion": "^1.0.14", "@gluestack-ui/actionsheet": "^0.2.53", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.26", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/radio": "^0.1.40", "@gluestack-ui/toast": "^1.0.9", "@gorhom/bottom-sheet": "^5.2.6", "@hookform/resolvers": "^5.1.1", "@legendapp/motion": "^2.4.0", "@react-native-async-storage/async-storage": "2.2.0", "@react-native-community/datetimepicker": "8.4.4", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/analytics": "22.4.0", "@react-native-firebase/app": "22.4.0", "@react-native-firebase/crashlytics": "22.4.0", "@react-native-firebase/messaging": "22.4.0", "@react-native-google-signin/google-signin": "^15.0.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@rn-primitives/slot": "^1.2.0", "@rn-primitives/tabs": "^1.2.0", "@rn-primitives/types": "^1.2.0", "@shopify/flash-list": "2.0.2", "@tanstack/react-query": "^5.81.5", "axios": "^1.10.0", "camelcase-keys": "^10.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "d3-shape": "^3.2.0", "dayjs": "^1.11.13", "expo": "^54.0.21", "expo-apple-authentication": "~8.0.7", "expo-asset": "~12.0.9", "expo-audio": "~1.0.14", "expo-auth-session": "~7.0.8", "expo-av": "~16.0.7", "expo-blur": "~15.0.7", "expo-build-properties": "~1.0.9", "expo-constants": "~18.0.10", "expo-crypto": "~15.0.7", "expo-dev-client": "~6.0.16", "expo-device": "~8.0.9", "expo-file-system": "~19.0.17", "expo-font": "~14.0.9", "expo-haptics": "~15.0.7", "expo-image": "~3.0.10", "expo-image-picker": "~17.0.8", "expo-linear-gradient": "~15.0.7", "expo-linking": "~8.0.8", "expo-localization": "~17.0.7", "expo-media-library": "~18.2.0", "expo-notifications": "~0.32.12", "expo-router": "~6.0.14", "expo-secure-store": "~15.0.7", "expo-sharing": "~14.0.7", "expo-splash-screen": "~31.0.10", "expo-status-bar": "~3.0.8", "expo-symbols": "~1.0.7", "expo-system-ui": "~6.0.8", "expo-updates": "~29.0.12", "expo-web-browser": "~15.0.8", "i18next": "^25.3.0", "immer": "^10.1.1", "lodash-es": "^4.17.21", "nativewind": "^4.2.1", "qs-esm": "^7.0.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-i18next": "^15.5.3", "react-native": "0.81.5", "react-native-awesome-gallery": "^0.4.3", "react-native-css-interop": "^0.2.1", "react-native-gesture-handler": "~2.28.0", "react-native-keyboard-controller": "1.18.5", "react-native-reanimated": "~4.1.1", "react-native-reanimated-carousel": "^4.0.3", "react-native-render-html": "^6.3.4", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-shadow-2": "^7.1.2", "react-native-size-scaling": "^0.5.4", "react-native-svg": "15.12.1", "react-native-toast-message": "2.3.0", "react-native-web": "^0.21.0", "react-native-webview": "13.15.0", "react-native-worklets": "0.5.1", "svg-path-bounds": "^1.0.2", "tailwind-merge": "^3.3.1", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/eslint-config": "^3.2.0", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/d3-shape": "^3.1.7", "@types/lodash-es": "^4.17.12", "@types/react": "~19.1.10", "@types/svg-path-bounds": "^1.0.2", "eslint": "9.39.1", "eslint-config-expo": "~10.0.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-unused-imports": "^4.3.0", "husky": "^9.1.7", "jscodeshift": "^0.15.2", "lint-staged": "^16.2.6", "patch-package": "^8.0.1", "postinstall-postinstall": "^2.1.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.5.14", "react-native-svg-transformer": "^1.5.1", "tailwindcss": "^3.4.17", "tsx": "^4.20.4", "typescript": "~5.9.2"}, "private": true, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"]}}