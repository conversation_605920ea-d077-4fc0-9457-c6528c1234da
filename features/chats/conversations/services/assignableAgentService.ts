import { httpService } from '@/services/http/http.service';
import { AssignableAgentAPIResponse, AssignableAgentResponse } from '../../types/Agent';
import { transformInboxAgent } from '../utils/camelCaseKey';

export class AssignableAgentService {
  static async getAgents(inboxIds: number[]): Promise<AssignableAgentResponse> {
    const response = await httpService.get<AssignableAgentAPIResponse>('assignable_agents', {
      params: {
        'inbox_ids[]': inboxIds,
      },
    });

    const inboxesAgents = response.payload.map(transformInboxAgent);
    return {
      agents: inboxesAgents,
      inboxIds,
    };
  }
}
