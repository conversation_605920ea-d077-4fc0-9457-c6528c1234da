
import { Contact } from "../../../../chats/types/Contact";
import { ContactSlice, ConversationStoreStateCreator } from "../type";

// G<PERSON><PERSON> định Contact.id là string (như chuẩn hóa)
const upsertOne = (state: { ids: number[]; entities: Record<string, Contact> }, contact: Contact) => {
    if (!state.entities[contact.id]) {
        state.ids.push(contact.id); // Đảm bảo ids là string[]
    }
    // Cập nhật bất biến nhờ Immer
    state.entities[contact.id] = contact;
};

export const createContactSlice: ConversationStoreStateCreator<ContactSlice> = (set, get) => ({
    ids: [],
    entities: {},

    // Tối ưu hóa: Trả về object mới nếu không cần Immer
    clearAllContacts: () => {
        set({ ids: [], entities: {} });
    },

    // Sử dụng upsertOne mô phỏng
    addContacts: (contacts: Contact[]) => {
        set(state => {
            contacts.forEach(contact => {
                upsertOne(state as any, contact); // Dùng 'as any' nếu có lỗi Type từ ConversationStoreStateCreator
            });
        });
    },

    addContact: (contact: Contact) => {
        if (contact) {
            set(state => {
                upsertOne(state as any, contact);
            });
        }
    },

    updateContact: (contact: Contact) => {
        if (contact) {
            set(state => {
                upsertOne(state as any, contact);
            });
        }
    },

    // SỬA LỖI LỒNG SET VÀ CẬP NHẬT TRẠNG THÁI BẤT BIẾN CỦA IMER
    updateContactsPresence: (contacts: Record<string, string>) => {
        set(state => {
            Object.keys(contacts).forEach(contactId => {
                // Kiểm tra xem Contact có tồn tại trong Entities không
                const entity = (state as any).entities[contactId];

                if (entity) {
                    const newAvailability = contacts[contactId];
                    // Cập nhật trạng thái trực tiếp (chỉ cần làm trong hàm set(state => {}))
                    entity.availabilityStatus = newAvailability;
                }
            });
        });
    },
});