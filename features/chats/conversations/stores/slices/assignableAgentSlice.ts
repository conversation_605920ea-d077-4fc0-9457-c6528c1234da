import { AssignableAgentService } from "../../services/assignableAgentService";
import { AssignableAgentSlice, ConversationStoreStateCreator } from "../type";

export const createAssignableAgentSlice: ConversationStoreStateCreator<AssignableAgentSlice> = (set, get) => ({
    records: {},
    uiFlags: {
        isLoading: false,
    },

    clearAssignableAgents: () => {
        set({ records: {} })
    },

    fetchAgents: async (inboxIds: number[]) => {
        set({ uiFlags: { isLoading: true } });

        try {
            const { agents } = await fetchAgentsApi(inboxIds);

            const key = inboxIds.join(',');
            set({ records: { ...get().records, [key]: agents }, uiFlags: { isLoading: false } })

        } catch (error) {
            console.error('Lỗi khi fetch agents:', error);

            set({ uiFlags: { isLoading: false } });

            throw error;
        }
    },
})


const fetchAgentsApi = async (inboxIds: number[]) => {
    try {
        const response = await AssignableAgentService.getAgents(inboxIds);
        return response;
    } catch (error) {
        const message = error instanceof Error ? error.message : '';
        throw new Error(message);
    }
}

