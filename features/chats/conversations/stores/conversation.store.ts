import { createStore, useStore as useZustandStore } from 'zustand';
import { ConversationStoreSelector, ConversationStoreShape } from "./type"
import { createAssignableAgentSlice } from "./slices/assignableAgentSlice"
import { use } from "react"
import { ConversationStoreContext } from "./context"

export const createConversationStore = () => {
    return createStore<ConversationStoreShape>((...arg) => {
        const assignableAgentSlice = createAssignableAgentSlice(...arg)
        return {
            ...assignableAgentSlice,
        }
    })
}


export function useConversationStoreSelector<T>(selector: ConversationStoreSelector<T>): T {
    // Get the store instance from context
    const storeContext = use(ConversationStoreContext);
  
    if (!storeContext) {
      throw new Error(
        'ConversationStoreContext must be used within a ConversationStoreContextProvider. '
        + 'Make sure to wrap your component tree with <ConversationStoreContextProvider>.',
      );
    }
  
    // Use Zust<PERSON>'s useStore hook with the selector
    return useZustandStore(storeContext, selector);
  }
  
  export const useConversationStoreContext = () => {
    return use(ConversationStoreContext);
  };