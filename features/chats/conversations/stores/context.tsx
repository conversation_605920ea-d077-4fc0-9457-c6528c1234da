import React, { createContext, useRef } from 'react'
import { createConversationStore } from './conversation.store'

type ConversationStoreProviderProps = {
  children: React.ReactNode
}
type ConversationStore = ReturnType<typeof createConversationStore>
export const ConversationStoreContext = createContext<ConversationStore | null>(null)

export const ConversationStoreProvider: React.FC<ConversationStoreProviderProps> = ({
  children,
}) => {
  const storeRef = useRef<ConversationStore | undefined>(undefined)

  // Create the store only if it doesn't exist
  if (!storeRef.current) {
    storeRef.current = createConversationStore()
  }

  return <ConversationStoreContext value={storeRef.current}>{children}</ConversationStoreContext>
}
