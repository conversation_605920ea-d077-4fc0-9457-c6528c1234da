import { StateCreator } from "zustand";
import { Agent } from "../../types/Agent";
import { Contact } from "../../types/Contact";


export type AssignableAgentSlice = {
    records: {
        [key: string]: Agent[];
    };
    uiFlags: {
        isLoading: boolean;
    };

    clearAssignableAgents: () => void;
    fetchAgents: (inboxIds: number[]) => Promise<void>;
}

export type ContactSlice = {
    ids: string[];

    entities: Record<string, Contact>;

    clearAllContacts: () => void;

    addContacts: (contacts: Contact[]) => void;

    addContact: (contact: Contact) => void;

    updateContact: (contact: Contact) => void;

    updateContactsPresence: (contacts: Record<string, string>) => void;
}

export type ConversationStoreShape =
    AssignableAgentSlice & ContactSlice



export type ConversationStoreStateCreator<T> = StateCreator<
    ConversationStoreShape,
    [],
    [],
    T
>;



export type ConversationStoreSelector<T> = (state: ConversationStoreShape) => T;

