
export interface SLAEvent {
    id: number;
    meta: object;
    eventType: string;
    createdAt: number;
    updatedAt: number;
  }
  
  
  export interface SLA {
    id: number;
    slaId: number;
    slaStatus: string;
    createdAt: number;
    updatedAt: number;
    slaDescription: string;
    slaName: string;
    slaFirstResponseTimeThreshold: number;
    slaNextResponseTimeThreshold: number;
    slaOnlyDuringBusinessHours: boolean;
    slaResolutionTimeThreshold: number;
  }