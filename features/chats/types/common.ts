export type Channel =
  | 'Channel::Whatsapp'
  | 'Channel::WebWidget'
  | 'Channel::TwitterProfile'
  | 'Channel::TwilioSms'
  | 'Channel::Telegram'
  | 'Channel::Sms'
  | 'Channel::Line'
  | 'Channel::FacebookPage'
  | 'Channel::Email'
  | 'Channel::Api'
  | 'Channel::All';


export type UserRole = 'administrator' | 'agent';
export type AvailabilityStatus = 'online' | 'offline' | 'busy' | 'typing';
export type ConversationPriority = null | 'low' | 'medium' | 'high' | 'urgent';
export type ConversationStatus = 'open' | 'resolved' | 'pending' | 'snoozed' | 'all';

