import { AvailabilityStatus, Channel, ConversationPriority, ConversationStatus, UserRole } from "./common";
import { Contact } from "./Contact";
import { Message } from "./Message";
import { SLA, SLAEvent } from "./SLA";
import { Team } from "./Team";

export interface Agent {
  id: number;
  accountId?: number | null;
  availabilityStatus?: AvailabilityStatus;
  autoOffline?: boolean;
  confirmed?: boolean;
  email?: string;
  availableName?: string | null;
  customAttributes?: object;
  name?: string | null;
  role?: UserRole;
  thumbnail?: string | null;
  type?: string;
}


export interface AssignableAgentResponse {
  agents: Agent[];
  inboxIds: number[];
}



export interface AssignableAgentAPIResponse {
  payload: Agent[];
}

export type UnixTimestamp = number;


export interface Conversation {
  accountId: number;
  additionalAttributes: ConversationAdditionalAttributes;
  agentLastSeenAt: UnixTimestamp;
  assigneeLastSeenAt: UnixTimestamp;
  canReply: boolean;
  contactLastSeenAt: UnixTimestamp;
  createdAt: UnixTimestamp;
  customAttributes: Record<string, string>;
  firstReplyCreatedAt: UnixTimestamp;
  id: number;
  inboxId: number;
  labels: string[];
  lastActivityAt: UnixTimestamp;
  muted: boolean;
  priority: ConversationPriority;
  snoozedUntil: UnixTimestamp | null;
  status: ConversationStatus;
  unreadCount: number;
  uuid: string;
  waitingSince: UnixTimestamp;

  channel?: Channel;

  messages: Message[];

  lastNonActivityMessage: Message | null;

  meta: ConversationMeta;

  // Deprecated
  timestamp: UnixTimestamp;

  slaPolicyId: number | null;

  appliedSla: SLA | null;

  slaEvents: SLAEvent[];
}

export interface ConversationAdditionalAttributes {
  browser?: {
    deviceName: string;
    browserName: string;
    platformName: string;
    browserVersion: string;
    platformVersion: string;
  };
  referer?: string;
  initiatedAt?: {
    timestamp: string;
  };
  browserLanguage?: string;
  type?: string;
}


export interface ConversationMeta {
  sender: Contact;
  assignee: Agent;
  team: Team | null;
  hmacVerified: boolean | null;
  channel: Channel;
}